import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';
import 'package:geolocator/geolocator.dart';
import 'package:geocoding/geocoding.dart';
import 'package:smart_kirana/services/maps_service.dart';
import 'package:smart_kirana/utils/constants.dart';
import 'package:smart_kirana/widgets/custom_button.dart';

class LocationPickerScreen extends StatefulWidget {
  static const String routeName = '/location-picker';
  final LatLng? initialLocation;

  const LocationPickerScreen({super.key, this.initialLocation});

  @override
  State<LocationPickerScreen> createState() => _LocationPickerScreenState();
}

class _LocationPickerScreenState extends State<LocationPickerScreen> {
  GoogleMapController? _mapController;
  final MapsService _mapsService = MapsService();

  LatLng? _selectedLocation;
  String _selectedAddress = 'Tap on map to select location';
  bool _isLoading = true;
  bool _isGettingAddress = false;
  String? _error;

  @override
  void initState() {
    super.initState();
    _selectedLocation = widget.initialLocation;
    _initializeMap();
  }

  @override
  void dispose() {
    _mapController?.dispose();
    super.dispose();
  }

  Future<void> _initializeMap() async {
    try {
      // Initialize maps service
      final initialized = await _mapsService.initialize();
      if (!initialized) {
        setState(() {
          _error = _mapsService.error ?? 'Failed to initialize maps';
          _isLoading = false;
        });
        return;
      }

      // If no initial location, try to get current location
      if (_selectedLocation == null) {
        await _getCurrentLocation();
      } else {
        await _getAddressFromLocation(_selectedLocation!);
      }

      setState(() {
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _error = 'Error initializing map: $e';
        _isLoading = false;
      });
    }
  }

  // Test geocoding service
  Future<bool> _testGeocodingService() async {
    try {
      // Test with a known location (India Gate, Delhi)
      List<Placemark> testPlacemarks = await placemarkFromCoordinates(
        28.6129,
        77.2295,
      ).timeout(const Duration(seconds: 5));

      if (kDebugMode) {
        debugPrint(
          'Geocoding test successful: ${testPlacemarks.length} placemarks found',
        );
        if (testPlacemarks.isNotEmpty) {
          debugPrint('Test placemark: ${testPlacemarks.first.locality}');
        }
      }
      return testPlacemarks.isNotEmpty;
    } catch (e) {
      if (kDebugMode) {
        debugPrint('Geocoding test failed: $e');
      }
      return false;
    }
  }

  // Alternative geocoding method with multiple attempts
  Future<String> _getAddressWithRetry(
    LatLng location, {
    int maxAttempts = 3,
  }) async {
    for (int attempt = 1; attempt <= maxAttempts; attempt++) {
      try {
        if (kDebugMode) {
          debugPrint(
            'Geocoding attempt $attempt for ${location.latitude}, ${location.longitude}',
          );
        }

        List<Placemark> placemarks = await placemarkFromCoordinates(
          location.latitude,
          location.longitude,
        ).timeout(Duration(seconds: 5 + (attempt * 2))); // Increasing timeout

        if (placemarks.isNotEmpty) {
          Placemark place = placemarks.first;

          // Try different formatting strategies
          List<String> addressParts = [];

          // Strategy 1: Use thoroughfare and subThoroughfare
          if (place.subThoroughfare != null &&
              place.subThoroughfare!.isNotEmpty) {
            if (place.thoroughfare != null && place.thoroughfare!.isNotEmpty) {
              addressParts.add(
                '${place.subThoroughfare}, ${place.thoroughfare}',
              );
            } else {
              addressParts.add(place.subThoroughfare!);
            }
          } else if (place.thoroughfare != null &&
              place.thoroughfare!.isNotEmpty) {
            addressParts.add(place.thoroughfare!);
          }

          // Strategy 2: Use name if no thoroughfare
          if (addressParts.isEmpty &&
              place.name != null &&
              place.name!.isNotEmpty) {
            addressParts.add(place.name!);
          }

          // Add locality information
          if (place.subLocality != null && place.subLocality!.isNotEmpty) {
            addressParts.add(place.subLocality!);
          }

          if (place.locality != null && place.locality!.isNotEmpty) {
            addressParts.add(place.locality!);
          } else if (place.subAdministrativeArea != null &&
              place.subAdministrativeArea!.isNotEmpty) {
            addressParts.add(place.subAdministrativeArea!);
          }

          if (place.administrativeArea != null &&
              place.administrativeArea!.isNotEmpty) {
            addressParts.add(place.administrativeArea!);
          }

          if (place.postalCode != null && place.postalCode!.isNotEmpty) {
            addressParts.add(place.postalCode!);
          }

          String result = addressParts.join(', ');
          if (result.isNotEmpty) {
            if (kDebugMode) {
              debugPrint('Geocoding successful on attempt $attempt: $result');
            }
            return result;
          }
        }

        if (kDebugMode) {
          debugPrint('Attempt $attempt: No meaningful address found');
        }

        // Wait before retry
        if (attempt < maxAttempts) {
          await Future.delayed(Duration(seconds: attempt));
        }
      } catch (e) {
        if (kDebugMode) {
          debugPrint('Attempt $attempt failed: $e');
        }

        // Wait before retry
        if (attempt < maxAttempts) {
          await Future.delayed(Duration(seconds: attempt));
        }
      }
    }

    // All attempts failed, return coordinates
    return 'Lat: ${location.latitude.toStringAsFixed(6)}, Lng: ${location.longitude.toStringAsFixed(6)}';
  }

  Future<void> _getCurrentLocation() async {
    try {
      // Check if location services are enabled
      bool serviceEnabled = await Geolocator.isLocationServiceEnabled();
      if (!serviceEnabled) {
        // Use default location (Delhi, India)
        _selectedLocation = const LatLng(28.6139, 77.2090);
        return;
      }

      // Check location permissions
      LocationPermission permission = await Geolocator.checkPermission();
      if (permission == LocationPermission.denied) {
        permission = await Geolocator.requestPermission();
        if (permission == LocationPermission.denied) {
          // Use default location
          _selectedLocation = const LatLng(28.6139, 77.2090);
          return;
        }
      }

      if (permission == LocationPermission.deniedForever) {
        // Use default location
        _selectedLocation = const LatLng(28.6139, 77.2090);
        return;
      }

      // Get current position
      Position position = await Geolocator.getCurrentPosition(
        desiredAccuracy: LocationAccuracy.medium,
        timeLimit: const Duration(seconds: 10),
      );

      _selectedLocation = LatLng(position.latitude, position.longitude);
      await _getAddressFromLocation(_selectedLocation!);
    } catch (e) {
      // Use default location on error
      _selectedLocation = const LatLng(28.6139, 77.2090);
    }
  }

  Future<void> _getAddressFromLocation(LatLng location) async {
    setState(() {
      _isGettingAddress = true;
    });

    try {
      // Use the retry mechanism for better reliability
      String address = await _getAddressWithRetry(location);

      if (mounted) {
        setState(() {
          _selectedAddress = address;
        });
      }
    } catch (e) {
      if (kDebugMode) {
        debugPrint('Geocoding error: $e');
      }
      if (mounted) {
        setState(() {
          _selectedAddress =
              'Lat: ${location.latitude.toStringAsFixed(6)}, Lng: ${location.longitude.toStringAsFixed(6)}';
        });

        // Show error message to user
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Could not get address: ${e.toString()}'),
            backgroundColor: AppColors.error,
            duration: const Duration(seconds: 3),
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isGettingAddress = false;
        });
      }
    }
  }

  void _onMapTap(LatLng location) {
    setState(() {
      _selectedLocation = location;
    });
    _getAddressFromLocation(location);
  }

  void _confirmLocation() {
    if (_selectedLocation != null) {
      Navigator.pop(context, {
        'location': _selectedLocation,
        'address': _selectedAddress,
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Select Location'),
        backgroundColor: AppColors.primary,
        foregroundColor: Colors.white,
        actions: [
          if (_selectedLocation != null)
            IconButton(
              icon: const Icon(Icons.my_location),
              onPressed: () async {
                await _getCurrentLocation();
                if (_selectedLocation != null && _mapController != null) {
                  _mapController!.animateCamera(
                    CameraUpdate.newLatLng(_selectedLocation!),
                  );
                }
              },
            ),
          // Debug button to test geocoding
          if (kDebugMode)
            IconButton(
              icon: const Icon(Icons.bug_report),
              onPressed: () async {
                bool result = await _testGeocodingService();
                if (mounted) {
                  ScaffoldMessenger.of(context).showSnackBar(
                    SnackBar(
                      content: Text(
                        'Geocoding test: ${result ? 'PASSED' : 'FAILED'}',
                      ),
                      backgroundColor: result ? Colors.green : Colors.red,
                    ),
                  );
                }
              },
            ),
        ],
      ),
      body:
          _isLoading
              ? const Center(child: CircularProgressIndicator())
              : _error != null
              ? Center(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Icon(Icons.error_outline, size: 64, color: AppColors.error),
                    const SizedBox(height: 16),
                    Text(
                      _error!,
                      textAlign: TextAlign.center,
                      style: const TextStyle(fontSize: 16),
                    ),
                    const SizedBox(height: 16),
                    ElevatedButton(
                      onPressed: () => Navigator.pop(context),
                      child: const Text('Go Back'),
                    ),
                  ],
                ),
              )
              : Column(
                children: [
                  // Map
                  Expanded(
                    child: GoogleMap(
                      initialCameraPosition: CameraPosition(
                        target:
                            _selectedLocation ?? const LatLng(28.6139, 77.2090),
                        zoom: 15.0,
                      ),
                      onMapCreated: (GoogleMapController controller) {
                        _mapController = controller;
                      },
                      onTap: _onMapTap,
                      markers:
                          _selectedLocation != null
                              ? {
                                Marker(
                                  markerId: const MarkerId('selected_location'),
                                  position: _selectedLocation!,
                                  icon: BitmapDescriptor.defaultMarkerWithHue(
                                    BitmapDescriptor.hueRed,
                                  ),
                                ),
                              }
                              : {},
                      myLocationEnabled: true,
                      myLocationButtonEnabled: false,
                      zoomControlsEnabled: true,
                      mapToolbarEnabled: false,
                      compassEnabled: true,
                      trafficEnabled: false,
                      buildingsEnabled: true,
                      indoorViewEnabled: false,
                      mapType: MapType.normal,
                    ),
                  ),

                  // Address display and confirm button
                  Container(
                    padding: const EdgeInsets.all(16),
                    decoration: BoxDecoration(
                      color: Colors.white,
                      boxShadow: [
                        BoxShadow(
                          color: Colors.black.withValues(alpha: 0.1),
                          blurRadius: 10,
                          offset: const Offset(0, -5),
                        ),
                      ],
                    ),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Row(
                          children: [
                            const Icon(
                              Icons.location_on,
                              color: AppColors.primary,
                              size: 20,
                            ),
                            const SizedBox(width: 8),
                            const Text(
                              'Selected Location',
                              style: TextStyle(
                                fontWeight: FontWeight.bold,
                                fontSize: 16,
                              ),
                            ),
                            if (_isGettingAddress) ...[
                              const SizedBox(width: 8),
                              const SizedBox(
                                width: 16,
                                height: 16,
                                child: CircularProgressIndicator(
                                  strokeWidth: 2,
                                ),
                              ),
                            ],
                          ],
                        ),
                        const SizedBox(height: 8),
                        Text(
                          _selectedAddress,
                          style: const TextStyle(fontSize: 14),
                        ),

                        // Show retry option if geocoding fails
                        if (_selectedAddress.startsWith('Lat:') &&
                            _selectedLocation != null) ...[
                          const SizedBox(height: 12),
                          Container(
                            padding: const EdgeInsets.all(12),
                            decoration: BoxDecoration(
                              color: Colors.orange.withValues(alpha: 0.1),
                              borderRadius: BorderRadius.circular(8),
                              border: Border.all(
                                color: Colors.orange.withValues(alpha: 0.3),
                              ),
                            ),
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Row(
                                  children: [
                                    Icon(
                                      Icons.warning,
                                      color: Colors.orange,
                                      size: 16,
                                    ),
                                    const SizedBox(width: 8),
                                    const Text(
                                      'Address not found',
                                      style: TextStyle(
                                        fontWeight: FontWeight.bold,
                                        color: Colors.orange,
                                      ),
                                    ),
                                  ],
                                ),
                                const SizedBox(height: 4),
                                const Text(
                                  'Geocoding service couldn\'t find an address for this location. You can still use the coordinates.',
                                  style: TextStyle(fontSize: 12),
                                ),
                                const SizedBox(height: 8),
                                Row(
                                  children: [
                                    Expanded(
                                      child: ElevatedButton.icon(
                                        onPressed:
                                            _isGettingAddress
                                                ? null
                                                : () => _getAddressFromLocation(
                                                  _selectedLocation!,
                                                ),
                                        icon: const Icon(
                                          Icons.refresh,
                                          size: 16,
                                        ),
                                        label: const Text('Retry'),
                                        style: ElevatedButton.styleFrom(
                                          backgroundColor: Colors.orange,
                                          foregroundColor: Colors.white,
                                          padding: const EdgeInsets.symmetric(
                                            vertical: 8,
                                          ),
                                        ),
                                      ),
                                    ),
                                  ],
                                ),
                              ],
                            ),
                          ),
                        ],

                        const SizedBox(height: 16),
                        CustomButton(
                          text: 'Confirm Location',
                          onPressed:
                              _selectedLocation != null
                                  ? _confirmLocation
                                  : null,
                          enabled:
                              _selectedLocation != null && !_isGettingAddress,
                        ),
                      ],
                    ),
                  ),
                ],
              ),
    );
  }
}
