{"edge": {"services": {"account_id": "00034001CE59CC55", "last_username": "<EMAIL>"}}, "edge_fundamentals_appdefaults": {"ess_lightweight_version": 101}, "ess_kv_states": {"restore_on_startup": {"closed_notification": false, "decrypt_success": true, "key": "restore_on_startup", "notification_popup_count": 0}, "startup_urls": {"closed_notification": false, "decrypt_success": true, "key": "startup_urls", "notification_popup_count": 0}, "template_url_data": {"closed_notification": false, "decrypt_success": true, "key": "template_url_data", "notification_popup_count": 0}}, "extensions": {"settings": {"ahfgeienlihckogmohjhadlkjgocpleb": {"account_extension_type": 0, "active_permissions": {"api": ["management", "system.display", "system.storage", "webstorePrivate", "system.cpu", "system.memory", "system.network"], "explicit_host": [], "manifest_permissions": [], "scriptable_host": []}, "app_launcher_ordinal": "t", "commands": {}, "content_settings": [], "creation_flags": 1, "disable_reasons": [], "events": [], "first_install_time": "*****************", "from_webstore": false, "incognito_content_settings": [], "incognito_preferences": {}, "last_update_time": "*****************", "location": 5, "manifest": {"app": {"launch": {"web_url": "https://chrome.google.com/webstore"}, "urls": ["https://chrome.google.com/webstore"]}, "description": "Discover extensions for Microsoft Edge.", "icons": {"128": "webstore_icon_128.png", "16": "webstore_icon_16.png"}, "key": "MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQCtl3tO0osjuzRsf6xtD2SKxPlTfuoy7AWoObysitBPvH5fE1NaAA1/2JkPWkVDhdLBWLaIBPYeXbzlHp3y4Vv/4XG+aN5qFE3z+1RU/NqkzVYHtIpVScf3DjTYtKVL66mzVGijSoAIwbFCC3LpGdaoe6Q1rSRDp76wR6jjFzsYwQIDAQAB", "name": "Web Store", "permissions": ["webstorePrivate", "management", "system.cpu", "system.display", "system.memory", "system.network", "system.storage"], "version": "0.2"}, "needs_sync": true, "page_ordinal": "n", "path": "C:\\Program Files (x86)\\Microsoft\\Edge\\Application\\136.0.3240.76\\resources\\web_store", "preferences": {}, "regular_only_preferences": {}, "was_installed_by_default": false, "was_installed_by_oem": false}, "ahokoikenoafgppiblgpenaaaolecifn": {"active_permissions": {"api": [], "explicit_host": [], "manifest_permissions": [], "scriptable_host": []}, "disable_reasons": [8192]}, "ajfboaconbpkglpfanbmlfgojgndmhmc": {"lastpingday": "*****************"}, "cjneempfhkonkkbcmnfdibgobmhbagaj": {"active_permissions": {"api": [], "explicit_host": [], "manifest_permissions": [], "scriptable_host": []}, "disable_reasons": [8192]}, "dcaajljecejllikfgbhjdgeognacjkkp": {"active_permissions": {"api": [], "explicit_host": [], "manifest_permissions": [], "scriptable_host": []}, "disable_reasons": [8192]}, "dgiklkfkllikcanfonkcabmbdfmgleag": {"account_extension_type": 0, "active_permissions": {"api": [], "explicit_host": [], "manifest_permissions": [], "scriptable_host": []}, "commands": {}, "content_settings": [], "creation_flags": 1, "disable_reasons": [], "events": [], "first_install_time": "*****************", "from_webstore": false, "incognito_content_settings": [], "incognito_preferences": {}, "last_update_time": "*****************", "location": 5, "manifest": {"content_capabilities": {"include_globs": ["https://*excel.officeapps.live.com/*", "https://*onenote.officeapps.live.com/*", "https://*powerpoint.officeapps.live.com/*", "https://*word-edit.officeapps.live.com/*", "https://*excel.officeapps.live.com.mcas.ms/*", "https://*onenote.officeapps.live.com.mcas.ms/*", "https://*word-edit.officeapps.live.com.mcas.ms/*", "https://*excel.partner.officewebapps.cn/*", "https://*onenote.partner.officewebapps.cn/*", "https://*powerpoint.partner.officewebapps.cn/*", "https://*word-edit.partner.officewebapps.cn/*", "https://*excel.gov.online.office365.us/*", "https://*onenote.gov.online.office365.us/*", "https://*powerpoint.gov.online.office365.us/*", "https://*word-edit.gov.online.office365.us/*", "https://*excel.dod.online.office365.us/*", "https://*onenote.dod.online.office365.us/*", "https://*powerpoint.dod.online.office365.us/*", "https://*word-edit.dod.online.office365.us/*", "https://*visio.partner.officewebapps.cn/*", "https://*visio.gov.online.office365.us/*", "https://*visio.dod.online.office365.us/*"], "matches": ["https://*.officeapps.live.com/*", "https://*.officeapps.live.com.mcas.ms/*", "https://*.partner.officewebapps.cn/*", "https://*.gov.online.office365.us/*", "https://*.dod.online.office365.us/*", "https://*.app.whiteboard.microsoft.com/*", "https://*.whiteboard.office.com/*", "https://*.app.int.whiteboard.microsoft.com/*", "https://*.whiteboard.office365.us/*", "https://*.dev.whiteboard.microsoft.com/*"], "permissions": ["clipboardRead", "clipboardWrite"]}, "default_locale": "en", "description": "This extension grants Microsoft web sites permission to read and write from the clipboard.", "key": "MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQCz4t/X7GeuP6GBpjmxndrjtzF//4CWeHlC68rkoV7hP3h5Ka6eX7ZMNlYJkSjmB5iRmPHO5kR1y7rGY8JXnRPDQh/CQNLVA7OsKeV6w+UO+vx8KGI+TrTAhzH8YGcMIsxsUjxtC4cBmprja+xDr0zVp2EMgqHu+GBKgwSRHTkDuwIDAQAB", "manifest_version": 2, "minimum_chrome_version": "77", "name": "Microsoft Clipboard Extension", "version": "1.0"}, "path": "C:\\Program Files (x86)\\Microsoft\\Edge\\Application\\136.0.3240.76\\resources\\edge_clipboard", "preferences": {}, "regular_only_preferences": {}, "was_installed_by_default": false, "was_installed_by_oem": false}, "ehlmnljdoejdahfjdfobmpfancoibmig": {"active_permissions": {"api": [], "explicit_host": [], "manifest_permissions": [], "scriptable_host": []}, "disable_reasons": [8192]}, "fdhgeoginicibhagdmblfikbgbkahibd": {"lastpingday": "*****************"}, "fikbjbembnmfhppjfnmfkahdhfohhjmg": {"account_extension_type": 0, "active_permissions": {"api": [], "explicit_host": [], "manifest_permissions": [], "scriptable_host": []}, "commands": {}, "content_settings": [], "creation_flags": 1, "disable_reasons": [], "events": [], "first_install_time": "*****************", "from_webstore": false, "incognito_content_settings": [], "incognito_preferences": {}, "last_update_time": "*****************", "location": 5, "manifest": {"background": {"persistent": false, "scripts": ["background.js"]}, "externally_connectable": {"matches": ["https://*.microsoftstream.com/*"]}, "incognito": "split", "key": "MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAsAmDrYmQaYQlLxSAn/jTQTGNt1IffJGIJeKucE/B42d8QIyFD2RCarmHP1bmbY1YuTng2dL3J//qyvUNwXPt9cmxH9WKwi512tzOa5r2zYaCuOgP2vAIrah/bKnpO3XmUfFWj+LRcbZahOmMDMQxzPKxFKuIz2eOiakBXDE6Ok7azHJ13LLQTte1JgZIPmyFrAciPABLp/IKLfsfnebVW1YgaOyxBNyp/7bhSmoyZI3kBv8InKOpGE8pttrBg6l5zkvD67a7ViNAYkqZIpJJV5ZTQtVWCWSG0xU2y+3zXZtx8KbGbDiWUAcwNYDVPpsV+IQXVpgAplHvrZme+hAl6QIDAQAB", "manifest_version": 2, "name": "Media Internals Services Extension", "permissions": ["mediaInternalsPrivate"], "version": "2.0.0"}, "path": "C:\\Program Files (x86)\\Microsoft\\Edge\\Application\\136.0.3240.76\\resources\\media_internals_services", "preferences": {}, "regular_only_preferences": {}, "was_installed_by_default": false, "was_installed_by_oem": false}, "fjngpfnaikknjdhkckmncgicobbkcnle": {"active_permissions": {"api": [], "explicit_host": [], "manifest_permissions": [], "scriptable_host": []}, "disable_reasons": [8192]}, "gbihlnbpmfkodghomcinpblknjhneknc": {"active_permissions": {"api": [], "explicit_host": [], "manifest_permissions": [], "scriptable_host": []}, "disable_reasons": [8192]}, "gbmoeijgfngecijpcnbooedokgafmmji": {"active_permissions": {"api": [], "explicit_host": [], "manifest_permissions": [], "scriptable_host": []}, "disable_reasons": [8192]}, "gcinnojdebelpnodghnoicmcdmamjoch": {"active_permissions": {"api": [], "explicit_host": [], "manifest_permissions": [], "scriptable_host": []}, "disable_reasons": [8192]}, "gecfnmoodchdkebjjffmdcmeghkflpib": {"active_permissions": {"api": [], "explicit_host": [], "manifest_permissions": [], "scriptable_host": []}, "disable_reasons": [8192]}, "gjekcakidljpnlncpmchpneehiindkic": {"lastpingday": "*****************"}, "hfmgbegjielnmfghmoohgmplnpeehike": {"active_permissions": {"api": [], "explicit_host": [], "manifest_permissions": [], "scriptable_host": []}, "disable_reasons": [8192]}, "iglcjdemknebjbklcgkfaebgojjphkec": {"account_extension_type": 0, "active_permissions": {"api": ["identity", "management", "metricsPrivate", "webstorePrivate", "hubPrivate"], "explicit_host": [], "manifest_permissions": [], "scriptable_host": []}, "app_launcher_ordinal": "w", "commands": {}, "content_settings": [], "creation_flags": 1, "disable_reasons": [], "events": [], "first_install_time": "*****************", "from_webstore": false, "incognito_content_settings": [], "incognito_preferences": {}, "last_update_time": "*****************", "location": 5, "manifest": {"app": {"launch": {"web_url": "https://microsoftedge.microsoft.com"}, "urls": ["https://microsoftedge.microsoft.com"]}, "description": "Discover extensions for Microsoft Edge.", "icons": {"128": "webstore_icon_128.png", "16": "webstore_icon_16.png"}, "key": "MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAtMvN4+y6cd3el/A/NT5eUnrz1WiD1WJRaJfMBvaMtJHIuFGEmYdYL/YuE74J19+pwhjOHeFZ3XUSMTdOa5moOaXXvdMr5wWaaN2frHewtAnNDO64NGbbZvdsfGm/kRkHKVGNV6dacZsAkylcz5CkwTmq97wOZ7ETaShHvhZEGwRQIt4K1poxurOkDYQw9ERZNf3fgYJ9ZTrLZMAFDLJY+uSF03pClWrr8VGc8LUQ4Naktb8QSgVUlrS14AdF/ESdbhnTvvdB0e7peNWRyoNtCqLJsbtTtBL6sOnqfusnwPowuueOFI+XskOT9TvLo6PcgxhLX5+d0mM+Jtn6PFTU8QIDAQAB", "name": "Microsoft Store", "permissions": ["webstorePrivate", "management", "metricsPrivate", "identity", "hubPrivate"], "version": "0.2"}, "needs_sync": true, "page_ordinal": "n", "path": "C:\\Program Files (x86)\\Microsoft\\Edge\\Application\\136.0.3240.76\\resources\\microsoft_web_store", "preferences": {}, "regular_only_preferences": {}, "was_installed_by_default": false, "was_installed_by_oem": false}, "ihmafllikibpmigkcoadcmckbfhibefp": {"account_extension_type": 0, "active_permissions": {"api": ["debugger", "feedbackPrivate", "fileSystem", "fileSystem.write", "app.window.fullscreen", "metricsPrivate", "storage", "tabs", "fileSystem.readFullPath", "edgeInternetConnectivityPrivate"], "explicit_host": ["edge://resources/*"], "manifest_permissions": [], "scriptable_host": []}, "commands": {}, "content_settings": [], "creation_flags": 1, "disable_reasons": [], "events": ["edgeFeedbackPrivate.onFeedbackRequested"], "first_install_time": "*****************", "from_webstore": false, "incognito_content_settings": [], "incognito_preferences": {}, "last_update_time": "*****************", "location": 5, "manifest": {"app": {"background": {"scripts": ["js/event_handler.js"]}, "content_security_policy": "default-src 'none'; script-src 'self' blob: filesystem: chrome://resources; style-src 'unsafe-inline' blob: chrome: file: filesystem: data: *; img-src * blob: chrome: file: filesystem: data:; media-src 'self' blob: filesystem:; connect-src data:"}, "description": "User feedback extension", "display_in_launcher": false, "display_in_new_tab_page": false, "icons": {"128": "images/icon128.png", "16": "images/icon16.png", "192": "images/icon192.png", "32": "images/icon32.png", "48": "images/icon48.png"}, "incognito": "split", "key": "MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAl3vxWwvLjcMIFK4OfG6C8PmJkMhFYDKRnx+SqG23YlMG1A+bOkiNmAN1TWpFPPp1f2PpbiZGNq1y29u/QfkD+PC4bnO7GbNw/2X5tGoP0n2K+KGGAxhnr0ki/oyo2eiFGSTOXlQvTRo5q1vB+Lbg+9TbFsWKlHZyAkeZ/YGz/iijHTqw8Q4RWdl5Tp8SlUhS/92EsWhveNJLW22veaT/Up2iSeSSwfyoHVYy8LUPaD4fbyLvPQacVLJq1dac2bNDqjaNvSPgPWCnkZtDmawZrgxT53otLCES/e96xfAf8I24VHIc1pVP8LqdqKr1AV1Yxn93h3VJ2QejtEhIAWHU6QIDAQAB", "manifest_version": 2, "name": "<PERSON>", "permissions": ["chrome://resources/", "debugger", "edgeInternetConnectivityPrivate", "feedbackPrivate", {"fileSystem": ["<PERSON><PERSON><PERSON><PERSON><PERSON>", "write"]}, "fullscreen", "metricsPrivate", "storage", "windows"], "version": "*******"}, "path": "C:\\Program Files (x86)\\Microsoft\\Edge\\Application\\136.0.3240.76\\resources\\edge_feedback", "preferences": {}, "regular_only_preferences": {}, "running": false, "was_installed_by_default": false, "was_installed_by_oem": false}, "jbleckejnaboogigodiafflhkajdmpcl": {"active_permissions": {"api": [], "explicit_host": [], "manifest_permissions": [], "scriptable_host": []}, "disable_reasons": [8192]}, "jdiccldimpdaibmpdkjnbmckianbfold": {"account_extension_type": 0, "active_permissions": {"api": ["activeTab", "metricsPrivate", "storage", "systemPrivate", "ttsEngine", "errorReporting"], "explicit_host": ["https://*.bing.com/*"], "manifest_permissions": [], "scriptable_host": []}, "commands": {}, "content_settings": [], "creation_flags": 1, "disable_reasons": [], "events": ["ttsEngine.onPause", "ttsEngine.onResume", "ttsEngine.onSpeak", "ttsEngine.onStop"], "first_install_time": "*****************", "from_webstore": false, "incognito_content_settings": [], "incognito_preferences": {}, "last_update_time": "*****************", "location": 5, "manifest": {"background": {"persistent": false, "scripts": ["lifetimeHelper.js", "telemetryHelper.js", "errorHelper.js", "voiceList/voiceListRequester.js", "voiceList/voiceListSingleton.js", "voiceList/voiceModel.js", "manifestHelper.js", "config.js", "ssml.js", "uuid.js", "wordBoundary.js", "audioStreamer.js", "wordBoundaryEventManager.js", "audioViewModel.js", "background.js"]}, "description": "Provides access to Microsoft's online text-to-speech voices", "key": "AAAAB3NzaC1yc2EAAAADAQABAAAAgQDjGOAV6/3fmEtQmFqlmqm5cZ+jlNhd6XikwMDp0I7BKh+AjG3aBIG/qqwlsF/7LAGatnSxBwUwZC0qMnGXtcOPVl26Q8OvMx0gt5Va5gxca+ae0Skluj9WN9TNxPFVhw21WbCt4D9q3kb+XXDlx/7v1ktYus4Fwr/skkjADG9cIQ==", "manifest_version": 2, "name": "Microsoft Voices", "permissions": ["activeTab", "errorReporting", "metricsPrivate", "storage", "systemPrivate", "ttsEngine", "https://*.bing.com/"], "tts_engine": {"voices": [{"codec": "audio-24khz-48kbitrate-mono-mp3", "event_types": ["end", "error", "start", "word"], "lang": "en-US", "remote": true, "server_name": "Microsoft Server Speech Text to Speech Voice (en-US, AriaNeural)", "voice_name": "Microsoft Aria Online (Natural) - English (United States)"}, {"codec": "audio-24khz-48kbitrate-mono-mp3", "event_types": ["end", "error", "start", "word"], "lang": "en-US", "remote": true, "server_name": "Microsoft Server Speech Text to Speech Voice (en-US, GuyNeural)", "voice_name": "Microsoft Guy Online (Natural) - English (United States)"}, {"codec": "audio-24khz-48kbitrate-mono-mp3", "event_types": ["end", "error", "start", "word"], "lang": "zh-CN", "remote": true, "server_name": "Microsoft Server Speech Text to Speech Voice (zh<PERSON><PERSON><PERSON>, XiaoxiaoNeural)", "voice_name": "Microsoft Xiaoxiao Online (Natural) - Chinese (Mainland)"}, {"codec": "audio-24khz-48kbitrate-mono-mp3", "event_types": ["end", "error", "start", "word"], "lang": "zh-CN", "remote": true, "server_name": "Microsoft Server Speech Text to Speech Voice (zh-CN, YunyangNeural)", "voice_name": "Microsoft Yunyang Online (Natural) - Chinese (Mainland)"}, {"codec": "audio-16khz-32kbitrate-mono-mp3", "event_types": ["end", "error", "start", "word"], "lang": "zh-TW", "remote": true, "server_name": "Microsoft Server Speech Text to Speech Voice (zh-TW, HanHanRUS)", "voice_name": "Microsoft HanHan Online - Chinese (Taiwan)"}, {"codec": "audio-16khz-32kbitrate-mono-mp3", "event_types": ["end", "error", "start", "word"], "lang": "zh-HK", "remote": true, "server_name": "Microsoft Server Speech Text to Speech Voice (zh-HK, TracyRUS)", "voice_name": "Microsoft Tracy Online - Chinese (Hong Kong)"}, {"codec": "audio-24khz-48kbitrate-mono-mp3", "event_types": ["end", "error", "start", "word"], "lang": "ja-<PERSON>", "remote": true, "server_name": "Microsoft Server Speech Text to Speech Voice (<PERSON><PERSON><PERSON><PERSON>, NanamiNeural)", "voice_name": "Microsoft Nanami Online (Natural) - Japanese (Japan)"}, {"codec": "audio-24khz-48kbitrate-mono-mp3", "event_types": ["end", "error", "start", "word"], "lang": "en-GB", "remote": true, "server_name": "Microsoft Server Speech Text to Speech Voice (en-GB, LibbyNeural)", "voice_name": "Microsoft Libby Online (Natural) - English (United Kingdom)"}, {"codec": "audio-24khz-48kbitrate-mono-mp3", "event_types": ["end", "error", "start", "word"], "lang": "pt-BR", "remote": true, "server_name": "Microsoft Server Speech Text to Speech Voice (pt-BR, FranciscaNeural)", "voice_name": "Microsoft Francisca Online (Natural) - Portuguese (Brazil)"}, {"codec": "audio-24khz-48kbitrate-mono-mp3", "event_types": ["end", "error", "start", "word"], "lang": "es-MX", "remote": true, "server_name": "Microsoft Server Speech Text to Speech Voice (es-MX, DaliaNeural)", "voice_name": "Microsoft Dalia Online (Natural) - Spanish (Mexico)"}, {"codec": "audio-16khz-32kbitrate-mono-mp3", "event_types": ["end", "error", "start", "word"], "lang": "en-IN", "remote": true, "server_name": "Microsoft Server Speech Text to Speech Voice (en-IN, PriyaRUS)", "voice_name": "Microsoft Priya Online - English (India)"}, {"codec": "audio-16khz-32kbitrate-mono-mp3", "event_types": ["end", "error", "start", "word"], "lang": "en-CA", "remote": true, "server_name": "Microsoft Server Speech Text to Speech Voice (en-CA, HeatherRUS)", "voice_name": "Microsoft Heather Online - English (Canada)"}, {"codec": "audio-24khz-48kbitrate-mono-mp3", "event_types": ["end", "error", "start", "word"], "lang": "fr-CA", "remote": true, "server_name": "Microsoft Server Speech Text to Speech Voice (fr-CA, SylvieNeural)", "voice_name": "Microsoft Sylvie Online (Natural) - French (Canada)"}, {"codec": "audio-24khz-48kbitrate-mono-mp3", "event_types": ["end", "error", "start", "word"], "lang": "fr-FR", "remote": true, "server_name": "Microsoft Server Speech Text to Speech Voice (fr-<PERSON><PERSON>, <PERSON>)", "voice_name": "Microsoft Denise Online (Natural) - French (France)"}, {"codec": "audio-24khz-48kbitrate-mono-mp3", "event_types": ["end", "error", "start", "word"], "lang": "de-DE", "remote": true, "server_name": "Microsoft Server Speech Text to Speech Voice (de-DE, KatjaNeural)", "voice_name": "Microsoft Katja Online (Natural) - German (Germany)"}, {"codec": "audio-16khz-32kbitrate-mono-mp3", "event_types": ["end", "error", "start", "word"], "lang": "ru-RU", "remote": true, "server_name": "Microsoft Server Speech Text to Speech Voice (ru-RU, EkaterinaRUS)", "voice_name": "Microsoft Ekaterina Online - Russian (Russia)"}, {"codec": "audio-16khz-32kbitrate-mono-mp3", "event_types": ["end", "error", "start", "word"], "lang": "en-AU", "remote": true, "server_name": "Microsoft Server Speech Text to Speech Voice (en-AU, HayleyRUS)", "voice_name": "Microsoft Hayley Online - English (Australia)"}, {"codec": "audio-24khz-48kbitrate-mono-mp3", "event_types": ["end", "error", "start", "word"], "lang": "it-IT", "remote": true, "server_name": "Microsoft Server Speech Text to Speech Voice (it-IT, ElsaNeural)", "voice_name": "Microsoft Elsa Online (Natural) - Italian (Italy)"}, {"codec": "audio-24khz-48kbitrate-mono-mp3", "event_types": ["end", "error", "start", "word"], "lang": "ko-KR", "remote": true, "server_name": "Microsoft Server Speech Text to Speech Voice (ko-KR, SunHiNeural)", "voice_name": "Microsoft SunHi Online (Natural) - Korean (Korea)"}, {"codec": "audio-16khz-32kbitrate-mono-mp3", "event_types": ["end", "error", "start", "word"], "lang": "nl-NL", "remote": true, "server_name": "Microsoft Server Speech Text to Speech Voice (nl-NL, HannaRUS)", "voice_name": "Microsoft Hanna Online - Dutch (Netherlands)"}, {"codec": "audio-24khz-48kbitrate-mono-mp3", "event_types": ["end", "error", "start", "word"], "lang": "es-ES", "remote": true, "server_name": "Microsoft Server Speech Text to Speech Voice (es-ES, ElviraNeural)", "voice_name": "Microsoft Elvira Online (Natural) - Spanish (Spain)"}, {"codec": "audio-24khz-48kbitrate-mono-mp3", "event_types": ["end", "error", "start", "word"], "lang": "tr-TR", "remote": true, "server_name": "Microsoft Server Speech Text to Speech Voice (tr-TR, EmelNeural)", "voice_name": "Microsoft Emel Online (Natural) - Turkish (Turkey)"}, {"codec": "audio-16khz-32kbitrate-mono-mp3", "event_types": ["end", "error", "start", "word"], "lang": "pl-PL", "remote": true, "server_name": "Microsoft Server Speech Text to Speech Voice (pl-PL, PaulinaRUS)", "voice_name": "Microsoft Paulina Online - Polish (Poland)"}]}, "version": "1.0"}, "path": "C:\\Program Files (x86)\\Microsoft\\Edge\\Application\\136.0.3240.76\\resources\\microsoft_voices", "preferences": {}, "regular_only_preferences": {}, "was_installed_by_default": false, "was_installed_by_oem": false}, "kfihiegbjaloebkmglnjnljoljgkkchm": {"active_permissions": {"api": [], "explicit_host": [], "manifest_permissions": [], "scriptable_host": []}, "disable_reasons": [8192]}, "kieiaeokhmlbffedbdfgbdcnhgpcckkn": {"active_permissions": {"api": [], "explicit_host": [], "manifest_permissions": [], "scriptable_host": []}, "disable_reasons": [8192]}, "lamehkegphbbfdailghaeeleoajilfho": {"lastpingday": "*****************"}, "mhjfbmdgcfjbbpaeojofohoefgiehjai": {"account_extension_type": 0, "active_permissions": {"api": ["contentSettings", "fileSystem", "fileSystem.write", "metricsPrivate", "tabs", "resourcesPrivate", "pdfViewerPrivate", "fileSystem.readFullPath", "errorReporting", "edgeLearningToolsPrivate", "fileSystem.getCurrentEntry", "edgePdfPrivate", "edgeCertVerifierPrivate"], "explicit_host": ["edge://resources/*", "edge://webui-test/*"], "manifest_permissions": [], "scriptable_host": []}, "commands": {}, "content_settings": [], "creation_flags": 1, "disable_reasons": [], "events": [], "first_install_time": "*****************", "from_webstore": false, "incognito_content_settings": [], "incognito_preferences": {}, "last_update_time": "*****************", "location": 5, "manifest": {"content_security_policy": "script-src 'self' 'wasm-eval' blob: filesystem: chrome://resources chrome://webui-test; object-src * blob: externalfile: file: filesystem: data:; trusted-types edge-internal fast-html pdf-url edge-pdf-static-policy;", "description": "", "incognito": "split", "key": "MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQDN6hM0rsDYGbzQPQfOygqlRtQgKUXMfnSjhIBL7LnReAVBEd7ZmKtyN2qmSasMl4HZpMhVe2rPWVVwBDl6iyNE/Kok6E6v6V3vCLGsOpQAuuNVye/3QxzIldzG/jQAdWZiyXReRVapOhZtLjGfywCvlWq7Sl/e3sbc0vWybSDI2QIDAQAB", "manifest_version": 2, "mime_types": ["application/pdf"], "mime_types_handler": "edge_pdf/index.html", "name": "Microsoft Edge PDF Viewer", "offline_enabled": true, "permissions": ["errorReporting", "chrome://resources/", "chrome://webui-test/", "contentSettings", "metricsPrivate", "edgeCertVerifierPrivate", "edgeLearningToolsPrivate", "edgePdfPrivate", "pdfViewerPrivate", "resourcesPrivate", "tabs", {"fileSystem": ["write", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "getCurrentEntry"]}], "version": "1"}, "path": "C:\\Program Files (x86)\\Microsoft\\Edge\\Application\\136.0.3240.76\\resources\\edge_pdf", "preferences": {}, "regular_only_preferences": {}, "was_installed_by_default": false, "was_installed_by_oem": false}, "ncbjelpjchkpbikbpkcchkhkblodoama": {"account_extension_type": 0, "active_permissions": {"api": [], "explicit_host": [], "manifest_permissions": [], "scriptable_host": []}, "commands": {}, "content_settings": [], "creation_flags": 1, "disable_reasons": [], "events": [], "first_install_time": "*****************", "from_webstore": false, "incognito_content_settings": [], "incognito_preferences": {}, "last_update_time": "*****************", "location": 5, "manifest": {"background": {"persistent": false, "scripts": ["background.js"]}, "externally_connectable": {"matches": ["https://*.teams.microsoft.com/*", "https://*.skype.com/*", "https://*.teams.live.com/*"]}, "incognito": "split", "key": "MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAtAdFAR3ckd5c7G8VSzUj4Ltt/QRInUOD00StG95LweksGcLBlFlYL46cHFVgHHj1gmzcpBtgsURdcrAC3V8yiE7GY4wtpOP+9l+adUGR+cyOG0mw9fLjyH+2Il0QqktsNXzkNiE1ogW4l0h4+PJc262j0vtm4hBzMvR0QScFWcAIcAErlUiWTt4jefXCAYqubV99ed5MvVMWBxe97wOa9hYwAhbCminOepA4RRTg9eyi0TiuHpq/bNI8C5qZgKIQNBAjgiFBaIx9hiMBFlK4NHUbFdgY6Qp/hSCMNurctwz1jpsXEnT4eHg1YWXfquoH8s4swIjkFCMBF6Ejc3cUkQIDAQAB", "manifest_version": 2, "name": "WebRTC Internals Extension", "permissions": ["webrtcInternalsPrivate"], "version": "2.0.2"}, "path": "C:\\Program Files (x86)\\Microsoft\\Edge\\Application\\136.0.3240.76\\resources\\webrtc_internals", "preferences": {}, "regular_only_preferences": {}, "was_installed_by_default": false, "was_installed_by_oem": false}, "nkbndigcebkoaejohleckhekfmcecfja": {"active_permissions": {"api": [], "explicit_host": [], "manifest_permissions": [], "scriptable_host": []}, "disable_reasons": [8192]}, "nkeimhogjdpnpccoofpliimaahmaaome": {"account_extension_type": 0, "active_permissions": {"api": ["processes", "webrtcLoggingPrivate", "system.cpu", "enterprise.hardwarePlatform"], "explicit_host": [], "manifest_permissions": [], "scriptable_host": []}, "commands": {}, "content_settings": [], "creation_flags": 1, "disable_reasons": [], "events": ["runtime.onConnectExternal"], "first_install_time": "*****************", "from_webstore": false, "incognito_content_settings": [], "incognito_preferences": {}, "last_update_time": "*****************", "location": 5, "manifest": {"background": {"page": "background.html", "persistent": false}, "externally_connectable": {"ids": ["moklfjoegmpoolceggbebbmgbddlhdgp", "ldmpofkllgeicjiihkimgeccbhghhmfj", "denipklgekfpcdmbahmbpnmokgajnhma", "kjfhgcncjdebkoofmbjoiemiboifnpbo", "ikfcpmgefdpheiiomgmhlmmkihchmdlj", "jlgegmdnodfhciolbdjciihnlaljdbjo", "lkbhffjfgpmpeppncnimiiikojibkhnm", "acdafoiapclbpdkhnighhilgampkglpc", "hkamnlhnogggfddmjomgbdokdkgfelgg"], "matches": ["https://*.meet.teams.microsoft.com/*", "https://*.meet.teams.live.com/*", "https://*.meet.skype.com/*"]}, "incognito": "split", "key": "MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQDAQt2ZDdPfoSe/JI6ID5bgLHRCnCu9T36aYczmhw/tnv6QZB2I6WnOCMZXJZlRdqWc7w9jo4BWhYS50Vb4weMfh/I0On7VcRwJUgfAxW2cHB+EkmtI1v4v/OU24OqIa1Nmv9uRVeX0GjhQukdLNhAE6ACWooaf5kqKlCeK+1GOkQIDAQAB", "manifest_version": 2, "name": "WebRTC Extension", "permissions": ["enterprise.hardwarePlatform", "processes", "system.cpu", "webrtcLoggingPrivate"], "version": "1.3.24"}, "path": "C:\\Program Files (x86)\\Microsoft\\Edge\\Application\\136.0.3240.76\\resources\\hangout_services", "preferences": {}, "regular_only_preferences": {}, "was_installed_by_default": false, "was_installed_by_oem": false}, "ofefcgjbeghpigppfmkologfjadafddi": {"active_permissions": {"api": [], "explicit_host": [], "manifest_permissions": [], "scriptable_host": []}, "disable_reasons": [8192]}, "pdffkfellgipmhklpdmokmckkkfcopbh": {"lastpingday": "*****************"}}}, "google": {"services": {"last_signed_in_username": "<EMAIL>"}}, "prefs": {"preference_reset_time": "*****************"}, "protection": {"macs": {"browser": {"show_home_button": "328A0894002D84B2605F851BA8E4BE64D207850665056335A2A06234C1668BBA"}, "default_search_provider_data": {"template_url_data": "CBDD32AAB2CA0E5015799C111ED3F53621FA1CCC15C05FACABDB75653E98F7FC"}, "edge": {"services": {"account_id": "35508B552B7615896DB72632622C2D1FC39F8DBBD4D714CBE90153EC2F70835E", "last_username": "272A316D6A16DDD79CC925C2A5F5F73D6F1162D83DB3E4438149A7FD27A9C33C"}}, "enterprise_signin": {"policy_recovery_token": "E4B573CEC84DFEAAD97C59A2C29AACE0A00565A521920A2D14A314E814AB6461"}, "extensions": {"settings": {"ahfgeienlihckogmohjhadlkjgocpleb": "5578D366B67230ADE71E20AF1A9C42DB71AEC823756EE4DC5EE8397A0E2FC7AD", "ahokoikenoafgppiblgpenaaaolecifn": "7DA3F631B8A73315073D2D81C7DADD59DB11E2F5D9A29689A36089F7C1C8F215", "ajfboaconbpkglpfanbmlfgojgndmhmc": "B95A2EAA2CDFC36C3A9C9B107219DF6713E4FF824953306E7BD9FA4B084688FE", "cjneempfhkonkkbcmnfdibgobmhbagaj": "E5873A8F4D6C03ECCF0B44CA32B9DB35BE80E3BDE187C761BEE523EE5F6E5172", "dcaajljecejllikfgbhjdgeognacjkkp": "461DF4A6FE8E839BDF0FDE0AF2C7873632BC36958DEBAB16F8B26EFA86AEF6D0", "dgiklkfkllikcanfonkcabmbdfmgleag": "3F5673E41DAB9520341CCBD810AF2D897AE6C5D36F80F19BFE8E9646251A25D9", "ehlmnljdoejdahfjdfobmpfancoibmig": "E3DD86F9265E04011B35F651B7B8DF8A6AB527DDB1DBBE3291783E918520AF8E", "fdhgeoginicibhagdmblfikbgbkahibd": "2A8F72059BBE1D55D1CD1E80F42AAC83A9E3F2FBC8D66041064A4C1AB99371B2", "fikbjbembnmfhppjfnmfkahdhfohhjmg": "CFDC77F199C45DA13A75F01B0681D56755EBF4EE0D8687891BD42AD917AA80B1", "fjngpfnaikknjdhkckmncgicobbkcnle": "A859215442AF527C5B4E19A2A3FE2844904CDED16A09EB1C602235AB6471715C", "gbihlnbpmfkodghomcinpblknjhneknc": "1EA73D65D0B5F86D1ED429C860ACD850D705EFC54D8DC64788D845E44218C552", "gbmoeijgfngecijpcnbooedokgafmmji": "BE0DA3D870BD96CC2A5A3A2FF4202A1C65DF0C0C7171B399E41C41D4D233EF04", "gcinnojdebelpnodghnoicmcdmamjoch": "C0951A953B373651291FE9688CFA57F660453FD07C4ED48A3F1DB8843D8CDABF", "gecfnmoodchdkebjjffmdcmeghkflpib": "34F4480ACB8DB5A804E6DAF2A7FBE5D4C21644F6FDA6B35E50496DA59589FB5E", "gjekcakidljpnlncpmchpneehiindkic": "810FCD31DB8CB56F482D2975B67402901F9AB789F89659EA3277E347306B8AD3", "hfmgbegjielnmfghmoohgmplnpeehike": "AD72F3CB1FFEFD9B6A5348E1432A5E9320853A810D81E5AF4B534518FA54BD10", "iglcjdemknebjbklcgkfaebgojjphkec": "64867D3A3D1CD13B085BF46BD8691871514A31E3CAAC08BF39C74CD0E180FBE6", "ihmafllikibpmigkcoadcmckbfhibefp": "077769D6ADE7BD603B44CBB3C28B765C2AED5CE1869265FC009C69F4D4040B71", "jbleckejnaboogigodiafflhkajdmpcl": "355AC1B90451EB9595CB14F69CE8671464493DFF4D3BDCFF33AC34D04C91ED63", "jdiccldimpdaibmpdkjnbmckianbfold": "A53FE8BFDAAFB85B80D5E5E5E5D4D709AAFE6A1EEB610F490AD5316F589E521A", "kfihiegbjaloebkmglnjnljoljgkkchm": "8656EA34C7F22A80212B711B31EE2936ED906124EACDAAADC633A412A75674DC", "kieiaeokhmlbffedbdfgbdcnhgpcckkn": "6570420714C46BBAD98392A4979B3656C72CA63FD2F4FBEAEB86A19A68CB26AF", "lamehkegphbbfdailghaeeleoajilfho": "5DC6583C9BAF85E2527DB1633FB71DC675D16B9B0F12C98146E1B91A93AFF233", "mhjfbmdgcfjbbpaeojofohoefgiehjai": "8DE04E13DE3C5B3D9543C0D1F78AE28CFC24D165B51A0214B564A15A5BFCC04B", "ncbjelpjchkpbikbpkcchkhkblodoama": "3BC9882EF1AC46BF8B7D0C51949DD50A0B047565A1CC07D6E422F5EB293B6E77", "nkbndigcebkoaejohleckhekfmcecfja": "445B8ABF58404E0DC244950B573B2AC9AF1AD715E5DE5AE4B5609731A2F6AEB0", "nkeimhogjdpnpccoofpliimaahmaaome": "20F2F1706E1C8B5B894FFC7CA68E739DBEF7DA6CCE8C555D10999B98A90BB306", "ofefcgjbeghpigppfmkologfjadafddi": "8CDD16B684EADED50451D9BD016E23C8E9C0F2CAB787E5276AD93CD7A0A6EAC4", "pdffkfellgipmhklpdmokmckkkfcopbh": "B6BF33880CD46B1161356DADBE0CEB6E6C6FDC98A66F4F746827AA7D86660AF1"}, "ui": {"developer_mode": "2F0116452B5A50F8577E77881DED089501D3D2E23BE770E967FE2E5F18D906E1"}}, "google": {"services": {"account_id": "1B2CB271A147645D41518244ED38100688F1DDB75B9BC7365D1568B21D010187", "last_signed_in_username": "09E35F17D696A54604AFCCA95102C9A7F6B7F1FE6F10384C3CB9C88313AE75F2", "last_username": "9FEDD0456F8E3B620E7376867180B0A282CCC681BD68B7B477FD94C14A597C61"}}, "homepage": "1C9DD58E5F2D7C5FC8F389D4B56BCBCE13B3431FFBB9A2217AD1AEC1CA893449", "homepage_is_newtabpage": "3B75E60AC2DC0873878FC7DBAE999D52CD9CE9C69504D45217DABDDDF206DF13", "media": {"cdm": {"origin_data": "28710754BB082786033E8AEFB3888EBDD1CDE06D9FE7BF925CD76288F50C30E1"}, "storage_id_salt": "292A9AFC48BF7FCF89BA7873AF527429E1CCBBB32AEB2D255CD574E23FF1A95B"}, "module_blocklist_cache_md5_digest": "26AA4E68ACBC2B31710C921103C36A6C516163D0BEE5EB62C4F8522F93ADBF74", "pinned_tabs": "5AD0B37026B54B8438B483FADD5CB5B6BCDD0C9090384E02CDC4C5C952CC5EB3", "prefs": {"preference_reset_time": "FCA84ABBB6863645C40A5765ED38F60C96F92AB5E00268A00C7CD1FE57904325"}, "safebrowsing": {"incidents_sent": "D4931F175AD9A88F2D1B2356D8E80E53A079E5528E11C8FC18229BB4265190EC"}, "search_provider_overrides": "2FFC89C2F673B3F38204EF4FB1663C8541143192EFF43895BEFE3D7414342520", "session": {"restore_on_startup": "258A6AD0359793C08260F85ED4E6A586C12D69DFE026253F14007E34AD38B335", "startup_urls": "90E0FD174A052BEFC64810E35E179D1C2A8745B498B840F708B282BF0919F4E2"}}, "super_mac": "65517EAC272FE11922059E250739BE81ECBF4F48ACD10036DF68DDB2ADB03993"}, "session": {"restore_on_startup": 5, "restore_on_startup_edge_lightweight": "59FA641665368DE286F4CC4ACB467DC12611372E23221DAE7023CE1476", "restore_on_startup_edge_lightweight_verify": "b2d7a00a6ad3f613108fd0f5ca0519a9", "startup_urls": ["https://mail.google.com/mail/u/0/#inbox"], "startup_urls_edge_lightweight": "A119AF8A73A4C5FE343ED30F6040445EE7DB558D37F53B69434CF113AA4DF848C3735E013FD60588D9114BDD5DA7058932D874BAB6ECF491CF2803EA35F524D14474D061A51BF2", "startup_urls_edge_lightweight_verify": "1f81504076a79e382ddb49fb4986eb55"}}